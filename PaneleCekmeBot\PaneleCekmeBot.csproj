﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <ApplicationIcon><PERSON>ahan-Ultrabuuf-Comics-Batman-Prefs.ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="Mattahan-Ultrabuuf-Comics-Batman-Prefs.ico" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="HtmlAgilityPack" Version="1.12.2" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.8" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog" Version="4.1.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
